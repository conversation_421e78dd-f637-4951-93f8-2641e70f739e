import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:stillpoint/main.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const StillpointApp());

    // Verify that our welcome screen shows the app name
    expect(find.text('Stillpoint'), findsOneWidget);
    expect(
      find.text('Your safe space for mental wellness and self-discovery'),
      findsOneWidget,
    );
  });
}
