import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:stillpoint/authentication/welcome_screen.dart';
import 'package:stillpoint/onboarding/name_screen.dart';
import 'package:stillpoint/onboarding/build_space_screen.dart';
import 'firebase_options.dart';

// Temporary main app screen for development
class TempMainScreen extends StatelessWidget {
  const TempMainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Stillpoint'), elevation: 0),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.primary.withValues(alpha: 0.05),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),

                // Welcome section
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.spa_outlined,
                        size: 48,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Welcome to Stillpoint',
                        style: theme.textTheme.displayMedium?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Your safe space for mental wellness and self-discovery',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Development notice
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.tertiary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: colorScheme.tertiary.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.code, color: colorScheme.tertiary, size: 20),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Development Mode: Authentication temporarily bypassed',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.tertiary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Navigation buttons
                _buildNavigationButton(
                  context,
                  'Test Onboarding Flow',
                  Icons.person_add_outlined,
                  colorScheme.primary,
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const NameScreen()),
                  ),
                ),

                const SizedBox(height: 16),

                _buildNavigationButton(
                  context,
                  'Build Your Space',
                  Icons.psychology_outlined,
                  colorScheme.secondary,
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const BuildSpaceScreen(),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                _buildNavigationButton(
                  context,
                  'Authentication Flow',
                  Icons.login_outlined,
                  colorScheme.tertiary,
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WelcomeScreen(),
                    ),
                  ),
                ),

                const Spacer(),

                // Footer
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Ready to grow into something amazing? 🌱',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        elevation: 3,
        shadowColor: color.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 24),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const StillpointApp());
}

class StillpointApp extends StatelessWidget {
  const StillpointApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Stillpoint',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.light(
          // Primary: Deep purple - sophisticated and calming
          primary: const Color(0xFF6366F1),
          onPrimary: Colors.white,

          // Secondary: Ocean blue - trustworthy and serene
          secondary: const Color(0xFF3B82F6),
          onSecondary: Colors.white,

          // Tertiary: Soft lavender - gentle and peaceful
          tertiary: const Color(0xFF8B5CF6),
          onTertiary: Colors.white,

          // Surface colors: Clean and modern
          surface: const Color(0xFFFAFAFA),
          onSurface: const Color(0xFF2C2C2C),

          // Error: Soft rose - gentle but noticeable
          error: const Color(0xFFEF4444),
          onError: Colors.white,

          // Additional accent colors
          outline: const Color(0xFFE0E0E0),
          surfaceContainerHighest: const Color(0xFFF5F5F5),
          onSurfaceVariant: const Color(0xFF666666),
        ),
        useMaterial3: true,

        // Enhanced typography
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.w300,
            letterSpacing: -0.5,
            color: Color(0xFF2C2C2C),
          ),
          displayMedium: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w400,
            letterSpacing: -0.25,
            color: Color(0xFF2C2C2C),
          ),
          headlineLarge: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w500,
            letterSpacing: 0,
            color: Color(0xFF2C2C2C),
          ),
          headlineMedium: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.15,
            color: Color(0xFF2C2C2C),
          ),
          titleLarge: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.15,
            color: Color(0xFF2C2C2C),
          ),
          bodyLarge: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.5,
            color: Color(0xFF444444),
          ),
          bodyMedium: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.25,
            color: Color(0xFF666666),
          ),
        ),

        // Enhanced button themes
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            elevation: 2,
            shadowColor: Colors.black26,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          ),
        ),

        // Card theme
        cardTheme: CardTheme(
          elevation: 4,
          shadowColor: Colors.black12,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),

        // App bar theme
        appBarTheme: const AppBarTheme(
          elevation: 0,
          centerTitle: true,
          backgroundColor: Color(0xFF6366F1),
          foregroundColor: Colors.white,
          titleTextStyle: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      home: const TempMainScreen(), // Temporarily bypassing WelcomeScreen
      routes: {
        '/name': (context) => const NameScreen(),
        '/build-space': (context) => const BuildSpaceScreen(),
      },
    );
  }
}
