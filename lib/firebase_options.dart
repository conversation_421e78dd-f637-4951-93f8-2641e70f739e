// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC4sP_KVv9YxOdeUnHx7jeqDaQrnk9mXPs',
    appId: '1:285610815208:android:34e1663d4816fb4fdaa35d',
    messagingSenderId: '285610815208',
    projectId: 'stillpointapp-d03a4',
    databaseURL: 'https://stillpointapp-d03a4-default-rtdb.firebaseio.com',
    storageBucket: 'stillpointapp-d03a4.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBQBwrrQVGWoOHM4aTus5IEecV4qiP26Ao',
    appId: '1:285610815208:ios:7e2e36ca02c6b2f6daa35d',
    messagingSenderId: '285610815208',
    projectId: 'stillpointapp-d03a4',
    databaseURL: 'https://stillpointapp-d03a4-default-rtdb.firebaseio.com',
    storageBucket: 'stillpointapp-d03a4.firebasestorage.app',
    androidClientId:
        '285610815208-k182fplnml8dqopsjaoqtopq8boubrhj.apps.googleusercontent.com',
    iosClientId:
        '285610815208-82irea3gfkts61bg0qlh5cdg6i3bbjos.apps.googleusercontent.com',
    iosBundleId: 'com.stillpointapp.stillpoint',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBQBwrrQVGWoOHM4aTus5IEecV4qiP26Ao',
    appId: '1:285610815208:ios:7e2e36ca02c6b2f6daa35d',
    messagingSenderId: '285610815208',
    projectId: 'stillpointapp-d03a4',
    databaseURL: 'https://stillpointapp-d03a4-default-rtdb.firebaseio.com',
    storageBucket: 'stillpointapp-d03a4.firebasestorage.app',
    androidClientId:
        '285610815208-k182fplnml8dqopsjaoqtopq8boubrhj.apps.googleusercontent.com',
    iosClientId:
        '285610815208-82irea3gfkts61bg0qlh5cdg6i3bbjos.apps.googleusercontent.com',
    iosBundleId: 'com.stillpointapp.stillpoint',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC4sP_KVv9YxOdeUnHx7jeqDaQrnk9mXPs',
    appId: '1:285610815208:web:34e1663d4816fb4fdaa35d',
    messagingSenderId: '285610815208',
    projectId: 'stillpointapp-d03a4',
    databaseURL: 'https://stillpointapp-d03a4-default-rtdb.firebaseio.com',
    storageBucket: 'stillpointapp-d03a4.firebasestorage.app',
    authDomain: 'stillpointapp-d03a4.firebaseapp.com',
  );
}
