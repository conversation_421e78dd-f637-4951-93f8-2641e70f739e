import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MoodTracker extends StatefulWidget {
  final String timeOfDay;

  const MoodTracker({super.key, required this.timeOfDay});

  @override
  State<MoodTracker> createState() => _MoodTrackerState();
}

class _MoodTrackerState extends State<MoodTracker>
    with TickerProviderStateMixin {
  double _moodValue = 3.0; // Default to middle (Okay)
  String? selectedMood;
  bool hasTrackedToday = false;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  final List<Map<String, dynamic>> moods = [
    {
      'name': 'Struggling',
      'emoji': '😢',
      'color': const Color(0xFFF44336),
      'description': 'Having a tough time',
      'value': 1.0,
    },
    {
      'name': 'Low',
      'emoji': '😔',
      'color': const Color(0xFFFF9800),
      'description': 'Feeling down',
      'value': 2.0,
    },
    {
      'name': 'Okay',
      'emoji': '😐',
      'color': const Color(0xFFFFEB3B),
      'description': 'Just okay',
      'value': 3.0,
    },
    {
      'name': 'Good',
      'emoji': '😊',
      'color': const Color(0xFF8BC34A),
      'description': 'Pretty good day',
      'value': 4.0,
    },
    {
      'name': 'Amazing',
      'emoji': '😄',
      'color': const Color(0xFF4CAF50),
      'description': 'Feeling fantastic!',
      'value': 5.0,
    },
  ];

  @override
  void initState() {
    super.initState();
    _checkIfTrackedToday();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _checkIfTrackedToday() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    final timeKey = '${dateKey}_${widget.timeOfDay}';

    final trackedMood = prefs.getString(timeKey);
    final trackedValue = prefs.getDouble('${timeKey}_value');

    setState(() {
      hasTrackedToday = trackedMood != null;
      selectedMood = trackedMood;
      if (trackedValue != null) {
        _moodValue = trackedValue;
      }
    });
  }

  Future<void> _saveMood(double value) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    final timeKey = '${dateKey}_${widget.timeOfDay}';

    // Find the mood that corresponds to this value
    final mood = moods.firstWhere((m) => m['value'] == value);

    await prefs.setString(timeKey, mood['name']);
    await prefs.setDouble('${timeKey}_value', value);

    setState(() {
      selectedMood = mood['name'];
      _moodValue = value;
      hasTrackedToday = true;
    });

    // Trigger celebration animation
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text('Mood tracked for this ${widget.timeOfDay}!'),
            ],
          ),
          backgroundColor: const Color(0xFF4CAF50),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currentMood = moods.firstWhere((m) => m['value'] == _moodValue);

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: hasTrackedToday ? _pulseAnimation.value : 1.0,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      hasTrackedToday
                          ? Icons.check_circle
                          : Icons.mood_outlined,
                      color:
                          hasTrackedToday
                              ? const Color(0xFF4CAF50)
                              : colorScheme.secondary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        hasTrackedToday
                            ? 'Mood tracked for this ${widget.timeOfDay}'
                            : 'How are you feeling this ${widget.timeOfDay}?',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color:
                              hasTrackedToday
                                  ? const Color(0xFF4CAF50)
                                  : colorScheme.secondary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Current mood display
                Center(
                  child: Column(
                    children: [
                      Text(
                        currentMood['emoji'],
                        style: const TextStyle(fontSize: 48),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        currentMood['name'],
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: currentMood['color'],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        currentMood['description'],
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),

                if (!hasTrackedToday) ...[
                  const SizedBox(height: 24),
                  Text(
                    'Slide to select your mood:',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  // Mood slider
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: currentMood['color'],
                      inactiveTrackColor: currentMood['color'].withValues(
                        alpha: 0.3,
                      ),
                      thumbColor: currentMood['color'],
                      overlayColor: currentMood['color'].withValues(alpha: 0.2),
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 12,
                      ),
                      trackHeight: 6,
                    ),
                    child: Slider(
                      value: _moodValue,
                      min: 1.0,
                      max: 5.0,
                      divisions: 4,
                      onChanged: (value) {
                        setState(() {
                          _moodValue = value;
                        });
                      },
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Track your mood button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _saveMood(_moodValue),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: currentMood['color'],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Track My Mood',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],

                if (hasTrackedToday) ...[
                  const SizedBox(height: 16),
                  Text(
                    'You can track your mood again next ${widget.timeOfDay}.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
