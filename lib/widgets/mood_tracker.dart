import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MoodTracker extends StatefulWidget {
  final String timeOfDay;
  
  const MoodTracker({super.key, required this.timeOfDay});

  @override
  State<MoodTracker> createState() => _MoodTrackerState();
}

class _MoodTrackerState extends State<MoodTracker> with TickerProviderStateMixin {
  String? selectedMood;
  bool hasTrackedToday = false;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  final List<Map<String, dynamic>> moods = [
    {
      'name': 'Amazing',
      'emoji': '😄',
      'color': const Color(0xFF4CAF50),
      'description': 'Feeling fantastic!'
    },
    {
      'name': 'Good',
      'emoji': '😊',
      'color': const Color(0xFF8BC34A),
      'description': 'Pretty good day'
    },
    {
      'name': 'Okay',
      'emoji': '😐',
      'color': const Color(0xFFFFEB3B),
      'description': 'Just okay'
    },
    {
      'name': 'Low',
      'emoji': '😔',
      'color': const Color(0xFFFF9800),
      'description': 'Feeling down'
    },
    {
      'name': 'Struggling',
      'emoji': '😢',
      'color': const Color(0xFFF44336),
      'description': 'Having a tough time'
    },
  ];

  @override
  void initState() {
    super.initState();
    _checkIfTrackedToday();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _checkIfTrackedToday() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    final timeKey = '${dateKey}_${widget.timeOfDay}';
    
    final trackedMood = prefs.getString(timeKey);
    
    setState(() {
      hasTrackedToday = trackedMood != null;
      selectedMood = trackedMood;
    });
  }

  Future<void> _saveMood(String mood) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month}-${today.day}';
    final timeKey = '${dateKey}_${widget.timeOfDay}';
    
    await prefs.setString(timeKey, mood);
    
    setState(() {
      selectedMood = mood;
      hasTrackedToday = true;
    });

    // Trigger celebration animation
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text('Mood tracked for this ${widget.timeOfDay}!'),
            ],
          ),
          backgroundColor: const Color(0xFF4CAF50),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: hasTrackedToday ? _pulseAnimation.value : 1.0,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      hasTrackedToday ? Icons.check_circle : Icons.mood_outlined,
                      color: hasTrackedToday ? const Color(0xFF4CAF50) : colorScheme.secondary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        hasTrackedToday 
                            ? 'Mood tracked for this ${widget.timeOfDay}'
                            : 'How are you feeling this ${widget.timeOfDay}?',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: hasTrackedToday ? const Color(0xFF4CAF50) : colorScheme.secondary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                if (hasTrackedToday && selectedMood != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Text(
                          moods.firstWhere((m) => m['name'] == selectedMood)['emoji'],
                          style: const TextStyle(fontSize: 32),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'You\'re feeling $selectedMood',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                moods.firstWhere((m) => m['name'] == selectedMood)['description'],
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  const SizedBox(height: 16),
                  Text(
                    'Tap on how you\'re feeling:',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 12,
                    runSpacing: 12,
                    children: moods.map((mood) {
                      return InkWell(
                        onTap: () => _saveMood(mood['name']),
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color: mood['color'].withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: mood['color'].withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                mood['emoji'],
                                style: const TextStyle(fontSize: 24),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                mood['name'],
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: mood['color'],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
                
                if (hasTrackedToday) ...[
                  const SizedBox(height: 16),
                  Text(
                    'You can track your mood again next ${widget.timeOfDay}.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
