import 'package:flutter/material.dart';

class CounselorSelectionScreen extends StatefulWidget {
  final String userName;

  const CounselorSelectionScreen({super.key, required this.userName});

  @override
  State<CounselorSelectionScreen> createState() =>
      _CounselorSelectionScreenState();
}

class _CounselorSelectionScreenState extends State<CounselorSelectionScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  final List<Map<String, dynamic>> counselors = [
    {
      'name': 'Dr. <PERSON>',
      'title': 'The Wise Mentor',
      'color': const Color(0xFF4CAF50), // Green
      'approach': 'Rooted in mindfulness, CBT, and stoic philosophy',
      'idealFor':
          'Users seeking clarity, emotional regulation, and personal growth',
      'quote': '"Pause. Reflect. There\'s power in your stillness."',
      'tone': 'Gentle, thought-provoking, and grounded',
      'specialty': 'Anxiety, burnout, life transitions, overthinking',
      'bio':
          'With a calm and centred presence, <PERSON><PERSON> <PERSON> will help you slow down, reflect deeply, and navigate challenges with clarity and self-awareness. Drawing from mindfulness, cognitive behavioural therapy, and timeless philosophy, <PERSON> encourages you to find wisdom in your struggles and peace in your progress.',
      'image': '🧘‍♂️',
      'imagePath': 'assets/images/counselors/dr_sage.png',
    },
    {
      'name': 'Luna',
      'title': 'The Compassionate Guide',
      'color': const Color(0xFFB39DDB), // Lilac
      'approach': 'Trauma-informed care, inner child work, gentle prompts',
      'idealFor': 'Users healing from emotional wounds or seeking self-love',
      'quote': '"You\'re not broken. You\'re becoming."',
      'tone': 'Warm, nurturing, deeply empathetic',
      'specialty': 'Trauma, self-esteem, relationships, emotional healing',
      'bio':
          'Whether you\'re healing from heartbreak, past trauma, or self-doubt, Luna offers a safe space to feel deeply and speak freely. Her voice is soothing, her words are soft, and her presence is nurturing. Luna believes in healing at your own pace and in the power of compassion—especially for yourself.',
      'image': '🌙',
      'imagePath': 'assets/images/counselors/luna.png',
    },
    {
      'name': 'Kai',
      'title': 'The Direct Motivator',
      'color': const Color(0xFFFF9800), // Orange
      'approach': 'Action-oriented, goal setting, mindset coaching',
      'idealFor': 'Those wanting accountability and structured guidance',
      'quote': '"Let\'s stop talking about it and take the first step."',
      'tone': 'Honest, encouraging, sometimes tough-love',
      'specialty': 'Productivity, career, ADHD, breaking patterns',
      'bio':
          'Kai doesn\'t sugarcoat. He\'ll show up with practical advice and the occasional push when you need it most. Whether you\'re feeling stuck, battling procrastination, or craving accountability, Kai is here to help you take action. His style is firm but always rooted in helping you build momentum, set goals, and improve your ways.',
      'image': '⚡',
      'imagePath': 'assets/images/counselors/kai.png',
    },
    {
      'name': 'Theo',
      'title': 'The Rational Listener',
      'color': const Color(0xFF2196F3), // Blue
      'approach': 'Logical reflection, emotional detachment for clarity',
      'idealFor': 'Users who want to make sense of their emotions logically',
      'quote': '"Let\'s understand the why before the what."',
      'tone': 'Neutral, focused, analytical',
      'specialty': 'Decision-making, conflict resolution, overthinking',
      'bio':
          'Theo brings clarity to emotional chaos. He\'s logical, unbiased, and focused on helping you understand your thoughts. If you\'re stuck in a loop, overanalysing, or unsure how to move forward, Theo will help you unpack things clearly and calmly. With a knack for asking the right questions, He\'ll empower you to solve problems, understand patterns, and gain a fresh perspective.',
      'image': '🤔',
      'imagePath': 'assets/images/counselors/theo.png',
    },
    {
      'name': 'Dr. Elena',
      'title': 'The Science-Based Whisperer',
      'color': const Color(0xFF2E7D32), // Deep green
      'approach': 'Evidence-based therapy, CBT, neuroscience-backed tools',
      'idealFor': 'Users wanting therapy rooted in results',
      'quote': '"You\'re not stuck—you just haven\'t learned the method yet."',
      'tone': 'Clear, methodical, supportive without fluff',
      'specialty':
          'Anxiety, cognitive distortions, habit change, emotional regulation',
      'bio':
          'Dr. Elena is your go-to when you want therapy that\'s rooted in science. Whether you\'re battling intrusive thoughts, anxiety or unhealthy habits, Elena breaks it all down in ways that actually make sense. She\'ll guide you with CBT techniques, brain-based insights, and strategies to help you build new pathways in your thinking and behaviour.',
      'image': '🧠',
      'imagePath': 'assets/images/counselors/dr_elena.png',
    },
    {
      'name': 'Zuri',
      'title': 'The Grounded Ally',
      'color': const Color(0xFFE91E63), // Pink
      'approach':
          'Identity-affirming therapy, trauma-informed, intersectional lens',
      'idealFor': 'Users navigating identity and cultural healing',
      'quote':
          '"You don\'t have to shrink to be safe. Your story belongs here."',
      'tone': 'Grounded, compassionate, socially aware',
      'specialty':
          'Identity & belonging, cultural trauma, racial stress, self-worth, intergenerational healing',
      'bio':
          'As someone grounded in cultural awareness and emotional depth, Zuri understands that healing isn\'t one-size-fits-all. Whether you\'re unpacking identity, navigating microaggressions, carrying generational trauma, Zuri creates space for your full self to exist and be seen. They blend emotional support with real-world perspective and help you reclaim your voice—without having to explain the basics.',
      'image': '✊🏾',
      'imagePath': 'assets/images/counselors/zuri.png',
    },
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _selectCounselor(Map<String, dynamic> counselor) {
    // TODO: Navigate to chat screen with selected counselor
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting session with ${counselor['name']}...'),
        backgroundColor: counselor['color'],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.primary.withValues(alpha: 0.05),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(
                            Icons.arrow_back,
                            color: colorScheme.primary,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Choose Your Counselor',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.w700,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Swipe to explore different counseling styles',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Page indicator
              Container(
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 24),
                child: Row(
                  children: List.generate(
                    counselors.length,
                    (index) => Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        decoration: BoxDecoration(
                          color:
                              index == _currentIndex
                                  ? counselors[_currentIndex]['color']
                                  : colorScheme.outline.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Counselor cards
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  itemCount: counselors.length,
                  itemBuilder: (context, index) {
                    final counselor = counselors[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: _buildCounselorCard(counselor, theme),
                    );
                  },
                ),
              ),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCounselorCard(Map<String, dynamic> counselor, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            counselor['color'].withValues(alpha: 0.1),
            counselor['color'].withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: counselor['color'].withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: counselor['color'].withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with image and name
            Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: counselor['color'].withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(child: _buildCounselorImage(counselor)),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        counselor['name'],
                        style: theme.textTheme.headlineSmall?.copyWith(
                          color: counselor['color'],
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        counselor['title'],
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: counselor['color'].withValues(alpha: 0.8),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Quote
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: counselor['color'].withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: counselor['color'].withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                counselor['quote'],
                style: theme.textTheme.titleMedium?.copyWith(
                  color: counselor['color'],
                  fontStyle: FontStyle.italic,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 20),

            // Specialty
            _buildInfoSection(
              'Specialty Areas',
              counselor['specialty'],
              counselor['color'],
              theme,
              Icons.psychology_outlined,
            ),

            const SizedBox(height: 16),

            // Approach
            _buildInfoSection(
              'Approach',
              counselor['approach'],
              counselor['color'],
              theme,
              Icons.lightbulb_outline,
            ),

            const SizedBox(height: 16),

            // Bio
            _buildInfoSection(
              'About ${counselor['name']}',
              counselor['bio'],
              counselor['color'],
              theme,
              Icons.person_outline,
            ),

            const SizedBox(height: 24),

            // Start session button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _selectCounselor(counselor),
                style: ElevatedButton.styleFrom(
                  backgroundColor: counselor['color'],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 4,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.chat_bubble_outline, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Start Session with ${counselor['name']}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    String title,
    String content,
    Color color,
    ThemeData theme,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildCounselorImage(Map<String, dynamic> counselor) {
    return FutureBuilder<bool>(
      future: _imageExists(counselor['imagePath']),
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data == true) {
          // Use actual image if it exists
          return ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.asset(
              counselor['imagePath'],
              width: 40,
              height: 40,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // Fallback to emoji if image fails to load
                return Text(
                  counselor['image'],
                  style: const TextStyle(fontSize: 40),
                );
              },
            ),
          );
        } else {
          // Use emoji as fallback
          return Text(counselor['image'], style: const TextStyle(fontSize: 40));
        }
      },
    );
  }

  Future<bool> _imageExists(String imagePath) async {
    try {
      await DefaultAssetBundle.of(context).load(imagePath);
      return true;
    } catch (e) {
      return false;
    }
  }
}
