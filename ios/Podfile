source 'https://cdn.cocoapods.org/'
platform :ios, '14.0'
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

# Load the Flutter pod helper
require File.expand_path(File.join('..', 'Flutter', 'podhelper.rb'), __FILE__)

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  # Install Flutter plugins (this will handle Firebase and Google Sign In)
  flutter_install_plugin_pods(
    flutter_root(File.expand_path('..', __FILE__)),
    File.expand_path('..', __FILE__),
    :ios
  )
end

target 'RunnerTests' do
  inherit! :search_paths
  use_modular_headers!
  pod 'GoogleSignIn', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
end

pre_install do |installer|
  # workaround for dependency validation
  Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'

      # For GoogleUtilities compatibility
      if target.name.include?('GoogleUtilities')
        config.build_settings['SWIFT_VERSION'] = '5.0'
      end
    end
  end

  # Workaround for Xcode 14.3+ compatibility
  installer.pods_project.build_configurations.each do |config|
    config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'i386'
  end
end